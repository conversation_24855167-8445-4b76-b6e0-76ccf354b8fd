{"version": 3, "sources": ["src_pages_personal-center_index_tsx-async.2360332716366602000.hot-update.js", "src/pages/personal-center/TeamListCard.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/personal-center/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='14407077971107833774';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/friend/index.tsx\":[\"p__friend__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"p__subscription__index\"],\"src/pages/team-management/index.tsx\":[\"src/pages/team-management/index.tsx\"],\"src/pages/team/detail/index.tsx\":[\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/user/index.tsx\":[\"p__user__index\"],\"src/pages/user/login/index.tsx\":[\"p__user__login__index\"],\"src/pages/user/team-select/index.tsx\":[\"src/pages/user/team-select/index.tsx\"]});;\r\n  },\r\n);\r\n", "import {\n  CarOutlined,\n  ClockCircleOutlined,\n  CrownOutlined,\n  ExclamationCircleOutlined,\n  RightOutlined,\n  TeamOutlined,\n  UserOutlined,\n} from '@ant-design/icons';\nimport { history, useModel } from '@umijs/max';\nimport {\n  Alert,\n  Card,\n  Col,\n  Flex,\n  List,\n  message,\n  Row,\n  Spin,\n  Tooltip,\n  Typography,\n} from 'antd';\nimport React, { useEffect, useState } from 'react';\nimport { AuthService } from '@/services';\nimport { TeamService } from '@/services/team';\nimport type { TeamDetailResponse } from '@/types/api';\nimport {\n  getTeamIdFromCurrentToken,\n  hasTeamInCurrentToken,\n} from '@/utils/tokenUtils';\n\nconst { Text, Title } = Typography;\n\n// 响应式布局样式\nconst styles = `\n  .team-item .ant-card-body {\n    padding: 0 !important;\n  }\n\n  .team-item:hover {\n    transform: translateY(-1px);\n    box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;\n  }\n\n  @media (max-width: 768px) {\n    .team-item {\n      margin-bottom: 8px;\n    }\n\n    .team-stats-row {\n      margin-top: 8px;\n    }\n  }\n\n  @media (max-width: 576px) {\n    .team-stats-row {\n      margin-top: 12px;\n    }\n\n    .team-stats-col {\n      margin-bottom: 4px;\n    }\n\n    .team-info-wrap {\n      gap: 8px !important;\n    }\n  }\n\n  @media (max-width: 480px) {\n    .team-name-text {\n      font-size: 14px !important;\n    }\n\n    .team-meta-text {\n      font-size: 11px !important;\n    }\n  }\n`;\n\nconst TeamListCard: React.FC = () => {\n  // 团队列表状态管理\n  const [teams, setTeams] = useState<TeamDetailResponse[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [switchingTeamId, setSwitchingTeamId] = useState<number | null>(null);\n\n  const { initialState, setInitialState } = useModel('@@initialState');\n  const currentTeam = initialState?.currentTeam;\n\n  // 获取当前Token中的团队信息，用于更准确的判断\n  const currentTokenTeamId = getTeamIdFromCurrentToken();\n  const hasTeamInToken = hasTeamInCurrentToken();\n\n  // 判断是否有真正的当前团队：需要同时满足initialState中有团队信息且Token中也有团队信息\n  const hasRealCurrentTeam = !!(\n    currentTeam &&\n    hasTeamInToken &&\n    currentTokenTeamId\n  );\n\n  // 获取实际的当前团队ID（优先使用Token中的信息，因为它是最新的）\n  const actualCurrentTeamId = hasRealCurrentTeam\n    ? currentTokenTeamId || currentTeam?.id\n    : null;\n\n  // 调试日志\n  console.log('TeamListCard 状态调试:', {\n    currentTeam: currentTeam?.id,\n    currentTokenTeamId,\n    hasTeamInToken,\n    hasRealCurrentTeam,\n    actualCurrentTeamId,\n    initialStateCurrentUser: !!initialState?.currentUser,\n  });\n\n  // 获取团队列表数据\n  useEffect(() => {\n    const fetchTeams = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n        const teamsData = await TeamService.getUserTeamsWithStats();\n        setTeams(teamsData);\n      } catch (error) {\n        console.error('获取团队列表失败:', error);\n        setError('获取团队列表失败');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    // 只有在用户已登录时才获取团队列表\n    if (initialState?.currentUser) {\n      fetchTeams();\n    }\n  }, [initialState?.currentUser]);\n\n  // 监听全局状态变化，处理注销等情况\n  useEffect(() => {\n    // 如果用户已注销（currentUser为undefined），清除本地团队列表状态\n    if (!initialState?.currentUser) {\n      setTeams([]);\n      setError(null);\n      setLoading(false);\n      setSwitchingTeamId(null);\n    }\n  }, [initialState?.currentUser]);\n\n  // 监听当前团队状态变化\n  useEffect(() => {\n    console.log('当前团队状态变化:', {\n      currentTeam: currentTeam?.id,\n      actualCurrentTeamId,\n      hasRealCurrentTeam,\n    });\n  }, [currentTeam?.id, actualCurrentTeamId, hasRealCurrentTeam]);\n\n  // 团队切换处理函数\n  const handleTeamSwitch = async (teamId: number, teamName: string) => {\n    // 检查用户是否已登录\n    if (!initialState?.currentUser) {\n      message.error('请先登录');\n      return;\n    }\n\n    try {\n      setSwitchingTeamId(teamId);\n\n      // 如果是当前团队，直接跳转到仪表盘，不需要调用切换API\n      if (teamId === actualCurrentTeamId) {\n        message.success(`进入团队：${teamName}`);\n        history.push('/dashboard');\n        return;\n      }\n\n      // 非当前团队，执行切换逻辑\n      const response = await AuthService.selectTeam({ teamId });\n\n      // 检查后端返回的团队选择成功标识\n      if (\n        response.teamSelectionSuccess &&\n        response.team &&\n        response.team.id === teamId\n      ) {\n        message.success(`已切换到团队：${teamName}`);\n\n        // 由于Token已经更新，路由守卫现在能够正确识别团队信息，可以直接跳转\n        // 同时异步更新 initialState 以保持状态同步\n        if (\n          initialState?.fetchTeamInfo &&\n          initialState?.fetchUserInfo &&\n          setInitialState\n        ) {\n          // 异步更新状态，不阻塞跳转\n          Promise.all([\n            initialState.fetchUserInfo(),\n            initialState.fetchTeamInfo(),\n          ])\n            .then(([currentUser, currentTeam]) => {\n              if (currentTeam && currentTeam.id === teamId) {\n                setInitialState({\n                  ...initialState,\n                  currentUser,\n                  currentTeam,\n                });\n              }\n            })\n            .catch((error) => {\n              console.error('更新 initialState 失败:', error);\n            });\n        }\n\n        // 直接跳转，路由守卫会处理团队验证\n        history.push('/dashboard');\n      } else {\n        console.error('团队切换响应异常，未返回正确的团队信息');\n        message.error('团队切换失败，请重试');\n      }\n    } catch (error) {\n      console.error('团队切换失败:', error);\n      message.error('团队切换失败');\n    } finally {\n      setSwitchingTeamId(null);\n    }\n  };\n\n  return (\n    <>\n      {/* 注入样式 */}\n      <style dangerouslySetInnerHTML={{ __html: styles }} />\n\n      <Card\n        className=\"dashboard-card\"\n        style={{\n          borderRadius: 16,\n          boxShadow: '0 6px 20px rgba(0,0,0,0.08)',\n          border: 'none',\n          background: 'linear-gradient(145deg, #ffffff, #f8faff)',\n        }}\n        title={\n          <Flex justify=\"space-between\" align=\"center\">\n            <Title\n              level={4}\n              style={{\n                margin: 0,\n                background: 'linear-gradient(135deg, #1890ff, #722ed1)',\n                WebkitBackgroundClip: 'text',\n                WebkitTextFillColor: 'transparent',\n                fontWeight: 600,\n              }}\n            >\n              团队列表\n            </Title>\n          </Flex>\n        }\n      >\n        {error ? (\n          <Alert\n            message=\"团队列表加载失败\"\n            description={error}\n            type=\"error\"\n            showIcon\n            style={{ marginBottom: 16 }}\n          />\n        ) : (\n          <Spin spinning={loading}>\n            {!initialState?.currentUser ? (\n              <div style={{ textAlign: 'center', padding: '40px 20px' }}>\n                <Text type=\"secondary\">请先登录以查看团队列表</Text>\n              </div>\n            ) : teams.length === 0 && !loading ? (\n              <div style={{ textAlign: 'center', padding: '40px 20px' }}>\n                <Text type=\"secondary\">暂无团队，请先加入或创建团队</Text>\n              </div>\n            ) : (\n              <List\n                dataSource={teams}\n                renderItem={(item) => (\n                  <List.Item>\n                    <Card\n                      className=\"team-item\"\n                      style={{\n                        background:\n                          actualCurrentTeamId === item.id\n                            ? 'linear-gradient(135deg, #f0f9ff, #e6f4ff, #f0f9ff)'\n                            : '#fff',\n                        borderRadius: 8,\n                        boxShadow:\n                          actualCurrentTeamId === item.id\n                            ? '0 4px 16px rgba(24, 144, 255, 0.2), 0 2px 8px rgba(82, 196, 26, 0.1)'\n                            : '0 1px 4px rgba(0,0,0,0.06)',\n                        width: '100%',\n                        borderLeft: actualCurrentTeamId === item.id\n                          ? `4px solid #1890ff`\n                          : `3px solid ${item.isCreator ? '#722ed1' : '#52c41a'}`,\n                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n                        border:\n                          actualCurrentTeamId === item.id\n                            ? '2px solid #91caff'\n                            : '1px solid #f0f0f0',\n                        padding: '12px 16px',\n                        position: 'relative',\n                        overflow: 'hidden',\n                      }}\n                      // 添加当前团队的特殊样式\n                      className={actualCurrentTeamId === item.id ? 'current-team-card' : ''}\n                      hoverable\n                      onMouseEnter={(e) => {\n                        if (actualCurrentTeamId !== item.id) {\n                          e.currentTarget.style.transform = 'translateY(-2px)';\n                          e.currentTarget.style.boxShadow =\n                            '0 8px 24px rgba(0,0,0,0.12)';\n                        }\n                      }}\n                      onMouseLeave={(e) => {\n                        if (actualCurrentTeamId !== item.id) {\n                          e.currentTarget.style.transform = 'translateY(0)';\n                          e.currentTarget.style.boxShadow =\n                            '0 2px 8px rgba(0,0,0,0.06)';\n                        }\n                      }}\n                    >\n                      {/* 响应式布局 */}\n                      <Row\n                        gutter={[8, 8]}\n                        align=\"middle\"\n                        style={{ width: '100%' }}\n                      >\n                        {/* 左侧：团队信息 */}\n                        <Col xs={24} sm={24} md={14} lg={12} xl={14}>\n                          <Flex vertical gap={6}>\n                            {/* 团队名称行 */}\n                            <Flex align=\"center\" gap={8} wrap=\"wrap\">\n                              <div\n                                style={{\n                                  cursor: 'pointer',\n                                  padding: '2px 4px',\n                                  borderRadius: 4,\n                                  transition: 'all 0.2s ease',\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  gap: 6,\n                                }}\n                                onClick={() =>\n                                  handleTeamSwitch(item.id, item.name)\n                                }\n                                onMouseEnter={(e) => {\n                                  e.currentTarget.style.background =\n                                    'rgba(24, 144, 255, 0.05)';\n                                }}\n                                onMouseLeave={(e) => {\n                                  e.currentTarget.style.background =\n                                    'transparent';\n                                }}\n                              >\n                                <Text\n                                  strong\n                                  style={{\n                                    fontSize: 16,\n                                    color:\n                                      actualCurrentTeamId === item.id\n                                        ? '#1890ff'\n                                        : '#262626',\n                                    lineHeight: 1.2,\n                                  }}\n                                >\n                                  {item.name}\n                                </Text>\n                                <RightOutlined\n                                  style={{\n                                    fontSize: 10,\n                                    color:\n                                      actualCurrentTeamId === item.id\n                                        ? '#1890ff'\n                                        : '#8c8c8c',\n                                    verticalAlign: 'middle',\n                                    display: 'inline-flex',\n                                    alignItems: 'center',\n                                  }}\n                                />\n                              </div>\n\n                              {/* 状态标识 */}\n                              {actualCurrentTeamId === item.id && (\n                                <span\n                                  style={{\n                                    background: 'linear-gradient(135deg, #1890ff, #52c41a)',\n                                    color: 'white',\n                                    padding: '2px 8px',\n                                    borderRadius: 12,\n                                    fontSize: 11,\n                                    fontWeight: 600,\n                                    boxShadow: '0 2px 4px rgba(24, 144, 255, 0.3)',\n                                    display: 'inline-flex',\n                                    alignItems: 'center',\n                                    gap: 3,\n                                  }}\n                                >\n                                  <span style={{\n                                    width: 6,\n                                    height: 6,\n                                    borderRadius: '50%',\n                                    backgroundColor: 'white',\n                                    display: 'inline-block'\n                                  }} />\n                                  当前团队\n                                </span>\n                              )}\n\n                              {switchingTeamId === item.id && (\n                                <Flex align=\"center\" gap={4}>\n                                  <Spin size=\"small\" />\n                                  <Text style={{ fontSize: 10, color: '#666' }}>\n                                    切换中\n                                  </Text>\n                                </Flex>\n                              )}\n                            </Flex>\n\n                            {/* 团队基本信息 */}\n                            <Flex align=\"center\" gap={12} wrap=\"wrap\">\n                              <Tooltip\n                                title={`创建时间: ${new Date(item.createdAt).toLocaleString('zh-CN')}`}\n                              >\n                                <Flex align=\"center\" gap={4}>\n                                  <ClockCircleOutlined\n                                    style={{ color: '#8c8c8c', fontSize: 12 }}\n                                  />\n                                  <Text\n                                    style={{ fontSize: 12, color: '#8c8c8c' }}\n                                  >\n                                    {new Date(\n                                      item.createdAt,\n                                    ).toLocaleDateString('zh-CN')}\n                                  </Text>\n                                </Flex>\n                              </Tooltip>\n\n                              <Tooltip\n                                title={`团队成员: ${item.memberCount}人`}\n                              >\n                                <Flex align=\"center\" gap={4}>\n                                  <TeamOutlined\n                                    style={{ color: '#8c8c8c', fontSize: 12 }}\n                                  />\n                                  <Text\n                                    style={{ fontSize: 12, color: '#8c8c8c' }}\n                                  >\n                                    {item.memberCount} 人\n                                  </Text>\n                                </Flex>\n                              </Tooltip>\n\n                              {/* 角色标识 */}\n                              <span\n                                style={{\n                                  background: item.isCreator\n                                    ? '#722ed1'\n                                    : '#52c41a',\n                                  color: 'white',\n                                  padding: '2px 6px',\n                                  borderRadius: 8,\n                                  fontSize: 10,\n                                  fontWeight: 500,\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  gap: 2,\n                                }}\n                              >\n                                {item.isCreator ? (\n                                  <>\n                                    <CrownOutlined style={{ fontSize: 9 }} />\n                                    管理员\n                                  </>\n                                ) : (\n                                  <>\n                                    <UserOutlined style={{ fontSize: 9 }} />\n                                    成员\n                                  </>\n                                )}\n                              </span>\n                            </Flex>\n                          </Flex>\n                        </Col>\n\n                        {/* 右侧：响应式指标卡片 */}\n                        <Col xs={24} sm={24} md={10} lg={12} xl={10}>\n                          <Row\n                            gutter={[4, 4]}\n                            justify={{ xs: 'start', md: 'end' }}\n                          >\n                            {/* 车辆资源 */}\n                            <Col xs={6} sm={6} md={6} lg={6} xl={6}>\n                              <div\n                                style={{\n                                  background: '#f0f7ff',\n                                  border: '1px solid #d9e8ff',\n                                  borderRadius: 6,\n                                  padding: '4px 6px',\n                                  textAlign: 'center',\n                                  minWidth: '45px',\n                                }}\n                              >\n                                <Flex vertical align=\"center\" gap={1}>\n                                  <CarOutlined\n                                    style={{ color: '#1890ff', fontSize: 12 }}\n                                  />\n                                  <Text\n                                    strong\n                                    style={{\n                                      fontSize: 14,\n                                      color: '#1890ff',\n                                      lineHeight: 1,\n                                    }}\n                                  >\n                                    {item.stats?.vehicles || 0}\n                                  </Text>\n                                  <Text style={{ fontSize: 8, color: '#666' }}>\n                                    车辆\n                                  </Text>\n                                </Flex>\n                              </div>\n                            </Col>\n\n                            {/* 人员资源 */}\n                            <Col xs={6} sm={6} md={6} lg={6} xl={6}>\n                              <div\n                                style={{\n                                  background: '#f6ffed',\n                                  border: '1px solid #d1f0be',\n                                  borderRadius: 6,\n                                  padding: '4px 6px',\n                                  textAlign: 'center',\n                                  minWidth: '45px',\n                                }}\n                              >\n                                <Flex vertical align=\"center\" gap={1}>\n                                  <UserOutlined\n                                    style={{ color: '#52c41a', fontSize: 12 }}\n                                  />\n                                  <Text\n                                    strong\n                                    style={{\n                                      fontSize: 14,\n                                      color: '#52c41a',\n                                      lineHeight: 1,\n                                    }}\n                                  >\n                                    {item.stats?.personnel || 0}\n                                  </Text>\n                                  <Text style={{ fontSize: 8, color: '#666' }}>\n                                    人员\n                                  </Text>\n                                </Flex>\n                              </div>\n                            </Col>\n\n                            {/* 临期事项 */}\n                            <Col xs={6} sm={6} md={6} lg={6} xl={6}>\n                              <div\n                                style={{\n                                  background: '#fff7e6',\n                                  border: '1px solid #ffd666',\n                                  borderRadius: 6,\n                                  padding: '4px 6px',\n                                  textAlign: 'center',\n                                  minWidth: '45px',\n                                }}\n                              >\n                                <Flex vertical align=\"center\" gap={1}>\n                                  <ExclamationCircleOutlined\n                                    style={{ color: '#faad14', fontSize: 12 }}\n                                  />\n                                  <Text\n                                    strong\n                                    style={{\n                                      fontSize: 14,\n                                      color: '#faad14',\n                                      lineHeight: 1,\n                                    }}\n                                  >\n                                    {item.stats?.expiring || 0}\n                                  </Text>\n                                  <Text style={{ fontSize: 8, color: '#666' }}>\n                                    临期\n                                  </Text>\n                                </Flex>\n                              </div>\n                            </Col>\n\n                            {/* 逾期事项 */}\n                            <Col xs={6} sm={6} md={6} lg={6} xl={6}>\n                              <div\n                                style={{\n                                  background: '#fff1f0',\n                                  border: '1px solid #ffccc7',\n                                  borderRadius: 6,\n                                  padding: '4px 6px',\n                                  textAlign: 'center',\n                                  minWidth: '45px',\n                                }}\n                              >\n                                <Flex vertical align=\"center\" gap={1}>\n                                  <ExclamationCircleOutlined\n                                    style={{ color: '#ff4d4f', fontSize: 12 }}\n                                  />\n                                  <Text\n                                    strong\n                                    style={{\n                                      fontSize: 14,\n                                      color: '#ff4d4f',\n                                      lineHeight: 1,\n                                    }}\n                                  >\n                                    {item.stats?.overdue || 0}\n                                  </Text>\n                                  <Text style={{ fontSize: 8, color: '#666' }}>\n                                    逾期\n                                  </Text>\n                                </Flex>\n                              </div>\n                            </Col>\n                          </Row>\n                        </Col>\n                      </Row>\n                    </Card>\n                  </List.Item>\n                )}\n              />\n            )}\n          </Spin>\n        )}\n      </Card>\n    </>\n  );\n};\n\nexport default TeamListCard;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uCACA;IACE,SAAS;;;;;;wCC0nBb;;;2BAAA;;;;;;0CArnBO;wCAC2B;yCAY3B;oFACoC;6CACf;yCACA;+CAKrB;;;;;;;;;;YAEP,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,gBAAU;YAElC,UAAU;YACV,MAAM,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2ChB,CAAC;YAED,MAAM,eAAyB;;gBAC7B,WAAW;gBACX,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAAuB,EAAE;gBAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;gBACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAAgB;gBAClD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAgB;gBAEtE,MAAM,EAAE,YAAY,EAAE,eAAe,EAAE,GAAG,IAAA,aAAQ,EAAC;gBACnD,MAAM,cAAc,yBAAA,mCAAA,aAAc,WAAW;gBAE7C,2BAA2B;gBAC3B,MAAM,qBAAqB,IAAA,qCAAyB;gBACpD,MAAM,iBAAiB,IAAA,iCAAqB;gBAE5C,qDAAqD;gBACrD,MAAM,qBAAqB,CAAC,CAC1B,CAAA,eACA,kBACA,kBAAiB;gBAGnB,qCAAqC;gBACrC,MAAM,sBAAsB,qBACxB,uBAAsB,wBAAA,kCAAA,YAAa,EAAE,IACrC;gBAEJ,OAAO;gBACP,QAAQ,GAAG,CAAC,sBAAsB;oBAChC,WAAW,EAAE,wBAAA,kCAAA,YAAa,EAAE;oBAC5B;oBACA;oBACA;oBACA;oBACA,yBAAyB,CAAC,EAAC,yBAAA,mCAAA,aAAc,WAAW;gBACtD;gBAEA,WAAW;gBACX,IAAA,gBAAS,EAAC;oBACR,MAAM,aAAa;wBACjB,IAAI;4BACF,WAAW;4BACX,SAAS;4BACT,MAAM,YAAY,MAAM,iBAAW,CAAC,qBAAqB;4BACzD,SAAS;wBACX,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,aAAa;4BAC3B,SAAS;wBACX,SAAU;4BACR,WAAW;wBACb;oBACF;oBAEA,mBAAmB;oBACnB,IAAI,yBAAA,mCAAA,aAAc,WAAW,EAC3B;gBAEJ,GAAG;oBAAC,yBAAA,mCAAA,aAAc,WAAW;iBAAC;gBAE9B,mBAAmB;gBACnB,IAAA,gBAAS,EAAC;oBACR,4CAA4C;oBAC5C,IAAI,EAAC,yBAAA,mCAAA,aAAc,WAAW,GAAE;wBAC9B,SAAS,EAAE;wBACX,SAAS;wBACT,WAAW;wBACX,mBAAmB;oBACrB;gBACF,GAAG;oBAAC,yBAAA,mCAAA,aAAc,WAAW;iBAAC;gBAE9B,aAAa;gBACb,IAAA,gBAAS,EAAC;oBACR,QAAQ,GAAG,CAAC,aAAa;wBACvB,WAAW,EAAE,wBAAA,kCAAA,YAAa,EAAE;wBAC5B;wBACA;oBACF;gBACF,GAAG;oBAAC,wBAAA,kCAAA,YAAa,EAAE;oBAAE;oBAAqB;iBAAmB;gBAE7D,WAAW;gBACX,MAAM,mBAAmB,OAAO,QAAgB;oBAC9C,YAAY;oBACZ,IAAI,EAAC,yBAAA,mCAAA,aAAc,WAAW,GAAE;wBAC9B,aAAO,CAAC,KAAK,CAAC;wBACd;oBACF;oBAEA,IAAI;wBACF,mBAAmB;wBAEnB,8BAA8B;wBAC9B,IAAI,WAAW,qBAAqB;4BAClC,aAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,SAAS,CAAC;4BAClC,YAAO,CAAC,IAAI,CAAC;4BACb;wBACF;wBAEA,eAAe;wBACf,MAAM,WAAW,MAAM,qBAAW,CAAC,UAAU,CAAC;4BAAE;wBAAO;wBAEvD,kBAAkB;wBAClB,IACE,SAAS,oBAAoB,IAC7B,SAAS,IAAI,IACb,SAAS,IAAI,CAAC,EAAE,KAAK,QACrB;4BACA,aAAO,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,SAAS,CAAC;4BAEpC,sCAAsC;4BACtC,8BAA8B;4BAC9B,IACE,CAAA,yBAAA,mCAAA,aAAc,aAAa,MAC3B,yBAAA,mCAAA,aAAc,aAAa,KAC3B,iBAEA,eAAe;4BACf,QAAQ,GAAG,CAAC;gCACV,aAAa,aAAa;gCAC1B,aAAa,aAAa;6BAC3B,EACE,IAAI,CAAC,CAAC,CAAC,aAAa,YAAY;gCAC/B,IAAI,eAAe,YAAY,EAAE,KAAK,QACpC,gBAAgB;oCACd,GAAG,YAAY;oCACf;oCACA;gCACF;4BAEJ,GACC,KAAK,CAAC,CAAC;gCACN,QAAQ,KAAK,CAAC,uBAAuB;4BACvC;4BAGJ,mBAAmB;4BACnB,YAAO,CAAC,IAAI,CAAC;wBACf,OAAO;4BACL,QAAQ,KAAK,CAAC;4BACd,aAAO,CAAC,KAAK,CAAC;wBAChB;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;wBACzB,aAAO,CAAC,KAAK,CAAC;oBAChB,SAAU;wBACR,mBAAmB;oBACrB;gBACF;gBAEA,qBACE;;sCAEE,2BAAC;4BAAM,yBAAyB;gCAAE,QAAQ;4BAAO;;;;;;sCAEjD,2BAAC,UAAI;4BACH,WAAU;4BACV,OAAO;gCACL,cAAc;gCACd,WAAW;gCACX,QAAQ;gCACR,YAAY;4BACd;4BACA,qBACE,2BAAC,UAAI;gCAAC,SAAQ;gCAAgB,OAAM;0CAClC,cAAA,2BAAC;oCACC,OAAO;oCACP,OAAO;wCACL,QAAQ;wCACR,YAAY;wCACZ,sBAAsB;wCACtB,qBAAqB;wCACrB,YAAY;oCACd;8CACD;;;;;;;;;;;sCAMJ,sBACC,2BAAC,WAAK;gCACJ,SAAQ;gCACR,aAAa;gCACb,MAAK;gCACL,QAAQ;gCACR,OAAO;oCAAE,cAAc;gCAAG;;;;;qDAG5B,2BAAC,UAAI;gCAAC,UAAU;0CACb,EAAC,yBAAA,mCAAA,aAAc,WAAW,kBACzB,2BAAC;oCAAI,OAAO;wCAAE,WAAW;wCAAU,SAAS;oCAAY;8CACtD,cAAA,2BAAC;wCAAK,MAAK;kDAAY;;;;;;;;;;2CAEvB,MAAM,MAAM,KAAK,KAAK,CAAC,wBACzB,2BAAC;oCAAI,OAAO;wCAAE,WAAW;wCAAU,SAAS;oCAAY;8CACtD,cAAA,2BAAC;wCAAK,MAAK;kDAAY;;;;;;;;;;yDAGzB,2BAAC,UAAI;oCACH,YAAY;oCACZ,YAAY,CAAC;4CA8OQ,aAiCA,cAiCA,cAiCA;6DAhVnB,2BAAC,UAAI,CAAC,IAAI;sDACR,cAAA,2BAAC,UAAI;gDACH,WAAU;gDACV,OAAO;oDACL,YACE,wBAAwB,KAAK,EAAE,GAC3B,uDACA;oDACN,cAAc;oDACd,WACE,wBAAwB,KAAK,EAAE,GAC3B,yEACA;oDACN,OAAO;oDACP,YAAY,wBAAwB,KAAK,EAAE,GACvC,CAAC,iBAAiB,CAAC,GACnB,CAAC,UAAU,EAAE,KAAK,SAAS,GAAG,YAAY,UAAU,CAAC;oDACzD,YAAY;oDACZ,QACE,wBAAwB,KAAK,EAAE,GAC3B,sBACA;oDACN,SAAS;oDACT,UAAU;oDACV,UAAU;gDACZ;gDACA,cAAc;gDACd,WAAW,wBAAwB,KAAK,EAAE,GAAG,sBAAsB;gDACnE,SAAS;gDACT,cAAc,CAAC;oDACb,IAAI,wBAAwB,KAAK,EAAE,EAAE;wDACnC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;wDAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAC7B;oDACJ;gDACF;gDACA,cAAc,CAAC;oDACb,IAAI,wBAAwB,KAAK,EAAE,EAAE;wDACnC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;wDAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAC7B;oDACJ;gDACF;0DAGA,cAAA,2BAAC,SAAG;oDACF,QAAQ;wDAAC;wDAAG;qDAAE;oDACd,OAAM;oDACN,OAAO;wDAAE,OAAO;oDAAO;;sEAGvB,2BAAC,SAAG;4DAAC,IAAI;4DAAI,IAAI;4DAAI,IAAI;4DAAI,IAAI;4DAAI,IAAI;sEACvC,cAAA,2BAAC,UAAI;gEAAC,QAAQ;gEAAC,KAAK;;kFAElB,2BAAC,UAAI;wEAAC,OAAM;wEAAS,KAAK;wEAAG,MAAK;;0FAChC,2BAAC;gFACC,OAAO;oFACL,QAAQ;oFACR,SAAS;oFACT,cAAc;oFACd,YAAY;oFACZ,SAAS;oFACT,YAAY;oFACZ,KAAK;gFACP;gFACA,SAAS,IACP,iBAAiB,KAAK,EAAE,EAAE,KAAK,IAAI;gFAErC,cAAc,CAAC;oFACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAC9B;gFACJ;gFACA,cAAc,CAAC;oFACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAC9B;gFACJ;;kGAEA,2BAAC;wFACC,MAAM;wFACN,OAAO;4FACL,UAAU;4FACV,OACE,wBAAwB,KAAK,EAAE,GAC3B,YACA;4FACN,YAAY;wFACd;kGAEC,KAAK,IAAI;;;;;;kGAEZ,2BAAC,oBAAa;wFACZ,OAAO;4FACL,UAAU;4FACV,OACE,wBAAwB,KAAK,EAAE,GAC3B,YACA;4FACN,eAAe;4FACf,SAAS;4FACT,YAAY;wFACd;;;;;;;;;;;;4EAKH,wBAAwB,KAAK,EAAE,kBAC9B,2BAAC;gFACC,OAAO;oFACL,YAAY;oFACZ,OAAO;oFACP,SAAS;oFACT,cAAc;oFACd,UAAU;oFACV,YAAY;oFACZ,WAAW;oFACX,SAAS;oFACT,YAAY;oFACZ,KAAK;gFACP;;kGAEA,2BAAC;wFAAK,OAAO;4FACX,OAAO;4FACP,QAAQ;4FACR,cAAc;4FACd,iBAAiB;4FACjB,SAAS;wFACX;;;;;;oFAAK;;;;;;;4EAKR,oBAAoB,KAAK,EAAE,kBAC1B,2BAAC,UAAI;gFAAC,OAAM;gFAAS,KAAK;;kGACxB,2BAAC,UAAI;wFAAC,MAAK;;;;;;kGACX,2BAAC;wFAAK,OAAO;4FAAE,UAAU;4FAAI,OAAO;wFAAO;kGAAG;;;;;;;;;;;;;;;;;;kFAQpD,2BAAC,UAAI;wEAAC,OAAM;wEAAS,KAAK;wEAAI,MAAK;;0FACjC,2BAAC,aAAO;gFACN,OAAO,CAAC,MAAM,EAAE,IAAI,KAAK,KAAK,SAAS,EAAE,cAAc,CAAC,SAAS,CAAC;0FAElE,cAAA,2BAAC,UAAI;oFAAC,OAAM;oFAAS,KAAK;;sGACxB,2BAAC,0BAAmB;4FAClB,OAAO;gGAAE,OAAO;gGAAW,UAAU;4FAAG;;;;;;sGAE1C,2BAAC;4FACC,OAAO;gGAAE,UAAU;gGAAI,OAAO;4FAAU;sGAEvC,IAAI,KACH,KAAK,SAAS,EACd,kBAAkB,CAAC;;;;;;;;;;;;;;;;;0FAK3B,2BAAC,aAAO;gFACN,OAAO,CAAC,MAAM,EAAE,KAAK,WAAW,CAAC,CAAC,CAAC;0FAEnC,cAAA,2BAAC,UAAI;oFAAC,OAAM;oFAAS,KAAK;;sGACxB,2BAAC,mBAAY;4FACX,OAAO;gGAAE,OAAO;gGAAW,UAAU;4FAAG;;;;;;sGAE1C,2BAAC;4FACC,OAAO;gGAAE,UAAU;gGAAI,OAAO;4FAAU;;gGAEvC,KAAK,WAAW;gGAAC;;;;;;;;;;;;;;;;;;0FAMxB,2BAAC;gFACC,OAAO;oFACL,YAAY,KAAK,SAAS,GACtB,YACA;oFACJ,OAAO;oFACP,SAAS;oFACT,cAAc;oFACd,UAAU;oFACV,YAAY;oFACZ,SAAS;oFACT,YAAY;oFACZ,KAAK;gFACP;0FAEC,KAAK,SAAS,iBACb;;sGACE,2BAAC,oBAAa;4FAAC,OAAO;gGAAE,UAAU;4FAAE;;;;;;wFAAK;;iHAI3C;;sGACE,2BAAC,mBAAY;4FAAC,OAAO;gGAAE,UAAU;4FAAE;;;;;;wFAAK;;;;;;;;;;;;;;;;;;;;;;;;;sEAUpD,2BAAC,SAAG;4DAAC,IAAI;4DAAI,IAAI;4DAAI,IAAI;4DAAI,IAAI;4DAAI,IAAI;sEACvC,cAAA,2BAAC,SAAG;gEACF,QAAQ;oEAAC;oEAAG;iEAAE;gEACd,SAAS;oEAAE,IAAI;oEAAS,IAAI;gEAAM;;kFAGlC,2BAAC,SAAG;wEAAC,IAAI;wEAAG,IAAI;wEAAG,IAAI;wEAAG,IAAI;wEAAG,IAAI;kFACnC,cAAA,2BAAC;4EACC,OAAO;gFACL,YAAY;gFACZ,QAAQ;gFACR,cAAc;gFACd,SAAS;gFACT,WAAW;gFACX,UAAU;4EACZ;sFAEA,cAAA,2BAAC,UAAI;gFAAC,QAAQ;gFAAC,OAAM;gFAAS,KAAK;;kGACjC,2BAAC,kBAAW;wFACV,OAAO;4FAAE,OAAO;4FAAW,UAAU;wFAAG;;;;;;kGAE1C,2BAAC;wFACC,MAAM;wFACN,OAAO;4FACL,UAAU;4FACV,OAAO;4FACP,YAAY;wFACd;kGAEC,EAAA,cAAA,KAAK,KAAK,cAAV,kCAAA,YAAY,QAAQ,KAAI;;;;;;kGAE3B,2BAAC;wFAAK,OAAO;4FAAE,UAAU;4FAAG,OAAO;wFAAO;kGAAG;;;;;;;;;;;;;;;;;;;;;;kFAQnD,2BAAC,SAAG;wEAAC,IAAI;wEAAG,IAAI;wEAAG,IAAI;wEAAG,IAAI;wEAAG,IAAI;kFACnC,cAAA,2BAAC;4EACC,OAAO;gFACL,YAAY;gFACZ,QAAQ;gFACR,cAAc;gFACd,SAAS;gFACT,WAAW;gFACX,UAAU;4EACZ;sFAEA,cAAA,2BAAC,UAAI;gFAAC,QAAQ;gFAAC,OAAM;gFAAS,KAAK;;kGACjC,2BAAC,mBAAY;wFACX,OAAO;4FAAE,OAAO;4FAAW,UAAU;wFAAG;;;;;;kGAE1C,2BAAC;wFACC,MAAM;wFACN,OAAO;4FACL,UAAU;4FACV,OAAO;4FACP,YAAY;wFACd;kGAEC,EAAA,eAAA,KAAK,KAAK,cAAV,mCAAA,aAAY,SAAS,KAAI;;;;;;kGAE5B,2BAAC;wFAAK,OAAO;4FAAE,UAAU;4FAAG,OAAO;wFAAO;kGAAG;;;;;;;;;;;;;;;;;;;;;;kFAQnD,2BAAC,SAAG;wEAAC,IAAI;wEAAG,IAAI;wEAAG,IAAI;wEAAG,IAAI;wEAAG,IAAI;kFACnC,cAAA,2BAAC;4EACC,OAAO;gFACL,YAAY;gFACZ,QAAQ;gFACR,cAAc;gFACd,SAAS;gFACT,WAAW;gFACX,UAAU;4EACZ;sFAEA,cAAA,2BAAC,UAAI;gFAAC,QAAQ;gFAAC,OAAM;gFAAS,KAAK;;kGACjC,2BAAC,gCAAyB;wFACxB,OAAO;4FAAE,OAAO;4FAAW,UAAU;wFAAG;;;;;;kGAE1C,2BAAC;wFACC,MAAM;wFACN,OAAO;4FACL,UAAU;4FACV,OAAO;4FACP,YAAY;wFACd;kGAEC,EAAA,eAAA,KAAK,KAAK,cAAV,mCAAA,aAAY,QAAQ,KAAI;;;;;;kGAE3B,2BAAC;wFAAK,OAAO;4FAAE,UAAU;4FAAG,OAAO;wFAAO;kGAAG;;;;;;;;;;;;;;;;;;;;;;kFAQnD,2BAAC,SAAG;wEAAC,IAAI;wEAAG,IAAI;wEAAG,IAAI;wEAAG,IAAI;wEAAG,IAAI;kFACnC,cAAA,2BAAC;4EACC,OAAO;gFACL,YAAY;gFACZ,QAAQ;gFACR,cAAc;gFACd,SAAS;gFACT,WAAW;gFACX,UAAU;4EACZ;sFAEA,cAAA,2BAAC,UAAI;gFAAC,QAAQ;gFAAC,OAAM;gFAAS,KAAK;;kGACjC,2BAAC,gCAAyB;wFACxB,OAAO;4FAAE,OAAO;4FAAW,UAAU;wFAAG;;;;;;kGAE1C,2BAAC;wFACC,MAAM;wFACN,OAAO;4FACL,UAAU;4FACV,OAAO;4FACP,YAAY;wFACd;kGAEC,EAAA,eAAA,KAAK,KAAK,cAAV,mCAAA,aAAY,OAAO,KAAI;;;;;;kGAE1B,2BAAC;wFAAK,OAAO;4FAAE,UAAU;4FAAG,OAAO;wFAAO;kGAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAmB/E;eA5iBM;;oBAOsC,aAAQ;;;iBAP9C;gBA8iBN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;ID1nBD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,8BAA6B;YAAC;SAAmB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;SAAyB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,kCAAiC;YAAC;SAAwB;QAAC,wCAAuC;YAAC;SAAuC;IAAA;;AACl3B"}